# Troubleshooting - Problemas de Conectividade

## 🚨 Erro "Failed to Fetch"

### <PERSON><PERSON>as
- E<PERSON> "failed to fetch" ao tentar fazer login
- Mensagem "Não foi possível conectar ao servidor"
- Timeout nas requisições

### Possíveis Causas e Soluções

#### 1. Backend não está rodando
**Verificação:**
```bash
# Verificar se o backend está rodando
curl http://localhost:8000/health
# ou
curl http://localhost:8000/api/v1/auth/login
```

**Solução:**
```bash
# Iniciar o backend
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 2. URL da API incorreta
**Verificação:**
- Verificar arquivo `frontend/.env`
- Confirmar se `VITE_API_URL` está correto

**Solução:**
```env
# frontend/.env
VITE_API_URL=http://localhost:8000/api/v1
```

#### 3. Problemas de CORS
**Sintomas:**
- Erro de CORS no console do navegador
- "Access-Control-Allow-Origin" error

**Solução no Backend:**
```python
# backend/app/main.py
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

#### 4. Firewall ou Antivírus
**Verificação:**
- Testar com firewall desabilitado temporariamente
- Verificar se antivírus está bloqueando conexões

**Solução:**
- Adicionar exceção para as portas 5173 e 8000
- Configurar regras de firewall

## 🔧 Modo Mock (Fallback Automático)

### O que é
Quando o backend não está disponível, o sistema automaticamente ativa o "Modo Mock" que simula as funcionalidades básicas usando dados locais.

### Como funciona
1. **Detecção automática**: Sistema tenta conectar ao backend
2. **Fallback**: Se falhar, ativa automaticamente o modo mock
3. **Indicador visual**: Mostra aviso de "Modo Offline" no canto superior direito
4. **Funcionalidades limitadas**: Login, registro e navegação básica funcionam

### Credenciais Mock
```
Username: testuser
Password: qualquer senha com 6+ caracteres

Username: admin  
Password: qualquer senha com 6+ caracteres
```

### Limitações do Modo Mock
- ❌ Dados não são salvos permanentemente
- ❌ Não há comunicação real com APIs
- ❌ Funcionalidades avançadas indisponíveis
- ❌ Upload de arquivos não funciona
- ❌ Modelos ML não podem ser treinados

## 🛠️ Ferramentas de Diagnóstico

### 1. Teste de Conectividade
- Acesse a página de login (`/auth`)
- Clique em "Testar Conectividade"
- Veja o status detalhado da conexão

### 2. Console do Navegador
Abra as ferramentas de desenvolvedor (F12) e verifique:
```javascript
// Logs do sistema
[VUR DEV] Login realizado
[VUR API] POST /auth/login

// Erros de rede
Failed to fetch
TypeError: NetworkError
```

### 3. Verificação Manual
```bash
# Testar backend diretamente
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}'
```

## 🔄 Soluções Rápidas

### Reiniciar Serviços
```bash
# Frontend
cd frontend
npm run dev

# Backend  
cd backend
uvicorn app.main:app --reload
```

### Limpar Cache
```javascript
// No console do navegador
localStorage.clear();
sessionStorage.clear();
location.reload();
```

### Forçar Modo Mock
```javascript
// No console do navegador
localStorage.setItem('vur_use_mock_api', 'true');
location.reload();
```

### Desabilitar Modo Mock
```javascript
// No console do navegador
localStorage.removeItem('vur_use_mock_api');
location.reload();
```

## 📋 Checklist de Verificação

### Backend
- [ ] Servidor rodando na porta 8000
- [ ] Endpoint `/health` responde
- [ ] Endpoint `/api/v1/auth/login` existe
- [ ] CORS configurado corretamente
- [ ] Logs do servidor sem erros

### Frontend
- [ ] Rodando na porta 5173
- [ ] Arquivo `.env` configurado
- [ ] `VITE_API_URL` correto
- [ ] Console sem erros de JavaScript
- [ ] Network tab mostra requisições

### Rede
- [ ] Firewall não está bloqueando
- [ ] Antivírus não está interferindo
- [ ] Proxy/VPN não está causando problemas
- [ ] DNS resolvendo localhost corretamente

## 🆘 Quando Pedir Ajuda

Se após seguir todos os passos o problema persistir, colete as seguintes informações:

### Informações do Sistema
- Sistema operacional
- Versão do Node.js (`node --version`)
- Versão do navegador
- Logs do console (F12 → Console)
- Logs do Network tab (F12 → Network)

### Logs do Backend
```bash
# Logs detalhados do FastAPI
uvicorn app.main:app --reload --log-level debug
```

### Configuração
- Conteúdo do arquivo `frontend/.env`
- Configuração de CORS no backend
- Estrutura de pastas do projeto

## 🎯 Prevenção

### Desenvolvimento
1. **Sempre verificar logs**: Console e terminal
2. **Testar endpoints**: Usar Postman ou curl
3. **Configurar CORS**: Desde o início do projeto
4. **Documentar URLs**: Manter .env atualizado

### Produção
1. **Health checks**: Implementar monitoramento
2. **Logs estruturados**: Para facilitar debug
3. **Fallbacks**: Ter planos B para falhas
4. **Alertas**: Notificações de problemas

## 📞 Contatos de Suporte

- **Documentação**: Verificar README.md
- **Issues**: Criar issue no repositório
- **Logs**: Sempre incluir logs relevantes
- **Reprodução**: Passos para reproduzir o problema
