# Funcionalidades de Desenvolvimento - VUR Frontend

## 🚀 Configurações de Desenvolvimento

O frontend VUR inclui várias funcionalidades para facilitar o desenvolvimento e testes. Todas essas funcionalidades são controladas por variáveis de ambiente e só aparecem em modo de desenvolvimento.

## ⚙️ Variáveis de Ambiente

### Configuração da API
```env
VITE_API_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/ws
```

### Funcionalidades de Desenvolvimento
```env
# Habilita valores padrão nos formulários (true/false)
VITE_DEV_DEFAULT_VALUES=true

# Habilita botões de login/registro rápido (true/false)
VITE_DEV_QUICK_ACTIONS=true

# Mostra avisos de desenvolvimento (true/false)
VITE_DEV_WARNINGS=true
```

### Credenciais Padrão
```env
VITE_DEV_USERNAME=testuser
VITE_DEV_PASSWORD=Test123456
VITE_DEV_EMAIL=<EMAIL>
VITE_DEV_FULLNAME=Usuário de Teste
```

## 🔐 Funcionalidades de Autenticação

### 1. Valores Padrão nos Formulários
- **Login**: Username e senha preenchidos automaticamente
- **Registro**: Todos os campos preenchidos com dados de teste
- **Configurável**: Pode ser desabilitado via `VITE_DEV_DEFAULT_VALUES=false`

### 2. Botões de Ação Rápida
- **Login Rápido**: Faz login instantâneo com credenciais padrão
- **Registro Rápido**: Cria conta instantaneamente com dados padrão
- **Visual**: Botões com ícone ⚡ e estilo diferenciado
- **Configurável**: Pode ser desabilitado via `VITE_DEV_QUICK_ACTIONS=false`

### 3. Avisos de Desenvolvimento
- **Alertas informativos**: Mostram as credenciais sendo usadas
- **Cores diferenciadas**: Azul para login, verde para registro
- **Informações úteis**: Exibem username, email e senha padrão
- **Configurável**: Pode ser desabilitado via `VITE_DEV_WARNINGS=false`

## 🎯 Como Usar

### Desenvolvimento Rápido
1. **Clone o projeto**
2. **Configure o .env** (copie do .env.example)
3. **Inicie o frontend**: `npm run dev`
4. **Acesse /auth** e use os botões rápidos

### Teste de Fluxos
1. **Login Rápido**: Clique no botão "⚡ Login Rápido (Dev)"
2. **Registro Rápido**: Clique no botão "⚡ Registro Rápido (Dev)"
3. **Teste Manual**: Use os campos preenchidos automaticamente

### Personalização
```typescript
// Modificar credenciais padrão
// Edite o arquivo .env ou frontend/src/config/development.ts

// Desabilitar funcionalidades
VITE_DEV_DEFAULT_VALUES=false  // Remove valores padrão
VITE_DEV_QUICK_ACTIONS=false   // Remove botões rápidos
VITE_DEV_WARNINGS=false        // Remove avisos
```

## 🔧 Configuração Avançada

### Arquivo de Configuração
O arquivo `frontend/src/config/development.ts` centraliza todas as configurações:

```typescript
export const DEV_CONFIG = {
  enableDefaultValues: true,
  enableQuickActions: true,
  showDevWarnings: true,
  defaultCredentials: {
    login: { username: 'testuser', password: 'Test123456' },
    register: { email: '<EMAIL>', ... }
  }
};
```

### Funções Utilitárias
```typescript
import { 
  getDefaultCredentials,
  shouldShowQuickActions,
  shouldShowDevWarnings,
  devLog,
  apiLog 
} from '@/config/development';

// Obter credenciais padrão
const creds = getDefaultCredentials();

// Verificar se deve mostrar botões rápidos
if (shouldShowQuickActions()) {
  // Renderizar botão rápido
}

// Log de desenvolvimento
devLog('Login realizado', { username: 'testuser' });
```

## 🚦 Estados Visuais

### Formulário de Login
- **Aviso azul**: Indica modo desenvolvimento
- **Campos preenchidos**: Username e senha padrão
- **Botão azul**: "⚡ Login Rápido (Dev)"
- **Placeholders informativos**: Mostram valores padrão

### Formulário de Registro
- **Aviso verde**: Indica modo desenvolvimento
- **Campos preenchidos**: Email, username, nome e senhas
- **Botão verde**: "⚡ Registro Rápido (Dev)"
- **Validação em tempo real**: Mostra força da senha

## 🔒 Segurança

### Apenas em Desenvolvimento
- Todas as funcionalidades só aparecem quando `import.meta.env.DEV === true`
- Em produção, os formulários funcionam normalmente
- Não há vazamento de credenciais em builds de produção

### Credenciais de Teste
- **Username**: testuser
- **Password**: Test123456 (atende todos os critérios de validação)
- **Email**: <EMAIL>
- **Nome**: Usuário de Teste

## 📝 Logs de Desenvolvimento

### API Calls
```typescript
// Logs automáticos de chamadas da API
[VUR API] POST /auth/login { username: 'testuser' }
[VUR API] GET /auth/me
```

### Debug Geral
```typescript
// Logs de desenvolvimento
[VUR DEV] Login realizado { username: 'testuser' }
[VUR DEV] Token salvo no localStorage
```

## 🎨 Customização Visual

### Cores dos Avisos
- **Login**: `border-blue-200 bg-blue-50 text-blue-800`
- **Registro**: `border-green-200 bg-green-50 text-green-800`

### Botões Rápidos
- **Login**: `border-blue-300 text-blue-600 hover:bg-blue-50`
- **Registro**: `border-green-300 text-green-600 hover:bg-green-50`

## 🚀 Produção

### Desabilitar Funcionalidades
Para produção, certifique-se de que:
```env
VITE_NODE_ENV=production
# ou simplesmente não defina as variáveis VITE_DEV_*
```

### Build de Produção
```bash
npm run build
```
- Remove automaticamente todas as funcionalidades de desenvolvimento
- Gera build otimizado sem código de debug
- Não inclui credenciais padrão

## 🔄 Fluxo de Desenvolvimento

1. **Desenvolvimento Local**
   - Funcionalidades ativas
   - Login/registro rápido
   - Logs detalhados

2. **Staging**
   - Funcionalidades opcionais
   - Testes com dados reais
   - Logs reduzidos

3. **Produção**
   - Funcionalidades desabilitadas
   - Formulários limpos
   - Sem logs de debug

## 📋 Checklist de Desenvolvimento

- [ ] Configurar .env com credenciais de teste
- [ ] Verificar se botões rápidos aparecem
- [ ] Testar login rápido
- [ ] Testar registro rápido
- [ ] Verificar avisos de desenvolvimento
- [ ] Testar fluxo manual
- [ ] Verificar logs no console
- [ ] Testar em modo produção

## 🆘 Troubleshooting

### Botões não aparecem
- Verificar se `VITE_DEV_QUICK_ACTIONS=true`
- Verificar se está em modo desenvolvimento
- Limpar cache do navegador

### Campos não preenchem
- Verificar se `VITE_DEV_DEFAULT_VALUES=true`
- Verificar variáveis de credenciais
- Recarregar página

### Avisos não mostram
- Verificar se `VITE_DEV_WARNINGS=true`
- Verificar se está em modo desenvolvimento
- Verificar console para erros
