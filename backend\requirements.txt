# ===================================
# VUR Backend - Python Dependencies (Essential Only)
# ===================================

# Core Framework
fastapi>=0.104.1
uvicorn[standard]>=0.24.0

# Database
sqlalchemy>=2.0.23
alembic>=1.12.1
psycopg2-binary>=2.9.9
asyncpg>=0.29.0

# Authentication & Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# Environment & Configuration
python-dotenv>=1.0.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# HTTP Client
httpx>=0.25.2
requests>=2.31.0

# Logging
structlog>=23.2.0

# Data Validation
email-validator>=2.1.0

# Data Processing & Analysis
pandas>=2.1.0
numpy>=1.24.0

# System Monitoring
psutil>=5.9.0

# File Processing
openpyxl>=3.1.0  # For Excel file support