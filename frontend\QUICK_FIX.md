# 🚨 Solução Rápida - Conta Sumiu / Modo Offline

## Problema: "A conta do usuário sumiu e está aparecendo apenas modo offline"

### ⚡ Solução Rápida (30 segundos)

#### Opção 1: Interface Gráfica
1. **Vá para a página de login** (`/auth`)
2. **Clique no botão "Reset Sistema"** (vermel<PERSON>, ao lado de "Testar Conectividade")
3. **<PERSON><PERSON> em "Reset Completo"**
4. **Aguarde o redirecionamento**

#### Opção 2: Console do Navegador
1. **Abra o DevTools** (F12)
2. **Vá para a aba Console**
3. **Digite e execute:**
```javascript
VUR_DEBUG.reset()
```

#### Opção 3: Manual (se as outras não funcionarem)
1. **Abra o DevTools** (F12)
2. **Vá para a aba Console**
3. **Digite e execute:**
```javascript
localStorage.clear();
window.location.href = '/auth';
```

---

## 🔍 Diagnóstico Rápido

### Verificar Status Atual
```javascript
// No console do navegador
VUR_DEBUG.status()
```

### Testar Conectividade
```javascript
// No console do navegador
VUR_DEBUG.testConnection()
```

---

## 🛠️ Soluções Específicas

### Se quer usar o Backend Real
```javascript
// No console do navegador
VUR_DEBUG.forceReal()
```

### Se quer continuar no Modo Mock
```javascript
// No console do navegador
VUR_DEBUG.forceMock()
```

### Se quer testar login
```javascript
// No console do navegador
VUR_DEBUG.testLogin()
```

---

## 📋 Checklist de Verificação

### ✅ Backend está rodando?
```bash
# No terminal do backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### ✅ Frontend está acessando a URL correta?
Verificar arquivo `frontend/.env`:
```env
VITE_API_URL=http://localhost:8000/api/v1
```

### ✅ CORS configurado no backend?
```python
# backend/app/main.py
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

---

## 🎯 Comandos de Debug Disponíveis

Abra o console (F12) e use:

```javascript
VUR_DEBUG.help()           // Mostrar ajuda
VUR_DEBUG.status()         // Status atual
VUR_DEBUG.reset()          // Reset completo
VUR_DEBUG.forceReal()      // Forçar API real
VUR_DEBUG.forceMock()      // Forçar modo mock
VUR_DEBUG.testConnection() // Testar backend
VUR_DEBUG.testLogin()      // Testar login
```

---

## 🚀 Fluxo Recomendado

### 1. Primeiro, tente reset rápido:
```javascript
VUR_DEBUG.reset()
```

### 2. Se não resolver, verifique backend:
```javascript
VUR_DEBUG.testConnection()
```

### 3. Se backend não estiver rodando:
- Inicie o backend
- Ou use modo mock: `VUR_DEBUG.forceMock()`

### 4. Se backend estiver rodando:
```javascript
VUR_DEBUG.forceReal()
```

---

## 🆘 Se Nada Funcionar

### Limpeza Completa Manual
```javascript
// No console do navegador
localStorage.clear();
sessionStorage.clear();
window.location.reload();
```

### Verificar Logs
1. **Console do navegador** (F12 → Console)
2. **Network tab** (F12 → Network)
3. **Logs do backend** (terminal onde está rodando)

### Reiniciar Tudo
```bash
# Parar frontend (Ctrl+C)
# Parar backend (Ctrl+C)

# Reiniciar backend
cd backend
uvicorn app.main:app --reload

# Reiniciar frontend
cd frontend
npm run dev
```

---

## 💡 Prevenção

### Para evitar o problema no futuro:

1. **Sempre fazer logout** antes de fechar o navegador
2. **Não fechar o backend** abruptamente durante desenvolvimento
3. **Usar o botão "Reset Sistema"** se notar comportamento estranho
4. **Verificar logs** regularmente no console

---

## 📞 Ainda com Problemas?

Se após seguir todos os passos o problema persistir:

1. **Colete informações:**
   - Output de `VUR_DEBUG.status()`
   - Logs do console (F12)
   - Logs do backend
   - Conteúdo do arquivo `.env`

2. **Tente em modo incógnito** para descartar problemas de cache

3. **Verifique se não há outros projetos** rodando nas mesmas portas

---

## 🎉 Sucesso!

Após resolver o problema:
- ✅ Você deve conseguir fazer login normalmente
- ✅ O indicador de "Modo Offline" deve desaparecer (se usando API real)
- ✅ Todas as funcionalidades devem estar disponíveis

**Credenciais padrão para teste:**
- Username: `testuser`
- Password: `Test123456`
