# 🚨 CORREÇÃO DE EMERGÊNCIA - Modo Offline Persistente

## ⚡ SOLUÇÃO IMEDIATA (Execute AGORA)

### Opção 1: Botão no Indicador
1. **Clique no botão "FORÇAR REAL"** no aviso laranja do canto superior direito

### Opção 2: <PERSON>sol<PERSON> do Navegador (MAIS EFETIVO)
1. **<PERSON><PERSON> DevTools** (F12)
2. **Vá para Console**
3. **Cole e execute este código:**

```javascript
// LIMPEZA COMPLETA E FORÇA API REAL
console.log('🚀 EXECUTANDO CORREÇÃO DE EMERGÊNCIA...');

// Limpar TUDO relacionado ao mock
localStorage.removeItem('vur_use_mock_api');
localStorage.removeItem('mock_user');
localStorage.removeItem('mock_token');
localStorage.removeItem('vur_auth_token');
localStorage.removeItem('vur_token_expiry');

// Limpar qualquer outro dado VUR/mock
Object.keys(localStorage).forEach(key => {
  if (key.startsWith('vur_') || key.startsWith('mock_')) {
    localStorage.removeItem(key);
  }
});

// Forçar uso da API real
localStorage.setItem('vur_force_real_api', 'true');

console.log('✅ LIMPEZA COMPLETA REALIZADA!');
console.log('🔄 REDIRECIONANDO...');

// Redirecionar para login
window.location.href = '/auth';
```

### Opção 3: Função Simplificada
```javascript
// Execute no console
FORCE_REAL_API()
```

---

## 🔍 VERIFICAR SE FUNCIONOU

Após executar a correção:

1. **Página deve recarregar** e ir para `/auth`
2. **Aviso laranja deve DESAPARECER**
3. **Fazer login normalmente**

### Se ainda aparecer o aviso:
```javascript
// Execute no console para verificar
console.log('Status localStorage:', Object.keys(localStorage).filter(k => k.includes('vur') || k.includes('mock')));
```

---

## 🛠️ SE AINDA NÃO FUNCIONOU

### Limpeza TOTAL:
```javascript
// ATENÇÃO: Isso limpa TUDO do localStorage
localStorage.clear();
sessionStorage.clear();
window.location.reload();
```

### Verificar se backend está rodando:
```javascript
// Testar conectividade
fetch('http://localhost:8000/health')
  .then(r => r.json())
  .then(data => console.log('✅ Backend OK:', data))
  .catch(e => console.log('❌ Backend OFF:', e));
```

---

## 📋 CHECKLIST FINAL

- [ ] Executei a limpeza no console
- [ ] Página redirecionou para `/auth`
- [ ] Aviso laranja desapareceu
- [ ] Consigo fazer login normalmente
- [ ] Backend está rodando (`http://localhost:8000`)

---

## 🚀 PREVENÇÃO

Para evitar que aconteça novamente:

1. **Sempre inicie o backend ANTES do frontend**
2. **Use logout antes de fechar o navegador**
3. **Se ver o aviso laranja, clique em "FORÇAR REAL" imediatamente**

---

## 📞 SE NADA FUNCIONAR

1. **Feche TODOS os navegadores**
2. **Reinicie o backend:**
   ```bash
   cd backend
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```
3. **Reinicie o frontend:**
   ```bash
   cd frontend
   npm run dev
   ```
4. **Execute a limpeza novamente**

---

## ✅ CREDENCIAIS PARA TESTE

Após a correção, use:
- **Username:** `testuser`
- **Password:** `Test123456`

Ou clique nos botões rápidos de desenvolvimento.
